import React, { useEffect, useState } from 'react';
import axios from 'axios';

function AdminDashboard() {
  const [foods, setFoods] = useState([]);
  const [form, setForm] = useState({ name: '', price: '', image: '', description: '' });

  const token = JSON.parse(localStorage.getItem('userInfo'))?.token;

  const fetchFoods = async () => {
    const { data } = await axios.get('http://localhost:5000/api/admin/foods', {
      headers: { Authorization: `Bearer ${token}` }
    });
    setFoods(data);
  };

  const addFood = async (e) => {
    e.preventDefault();
    await axios.post('http://localhost:5000/api/admin/foods', form, {
      headers: { Authorization: `Bearer ${token}` }
    });
    fetchFoods();
    setForm({ name: '', price: '', image: '', description: '' });
  };

  const deleteFood = async (id) => {
    await axios.delete(`http://localhost:5000/api/admin/foods/${id}`, {
      headers: { Authorization: `Bearer ${token}` }
    });
    fetchFoods();
  };

  useEffect(() => {
    fetchFoods();
  }, []);

  return (
    <div style={{ padding: '2rem' }}>
      <h2>Admin Panel - Food Management</h2>

      <form onSubmit={addFood} style={{ marginBottom: '2rem' }}>
        <input placeholder="Name" value={form.name} onChange={e => setForm({ ...form, name: e.target.value })} />
        <input placeholder="Price" value={form.price} onChange={e => setForm({ ...form, price: e.target.value })} />
        <input placeholder="Image URL" value={form.image} onChange={e => setForm({ ...form, image: e.target.value })} />
        <input placeholder="Description" value={form.description} onChange={e => setForm({ ...form, description: e.target.value })} />
        <button type="submit">Add Food</button>
      </form>

      <h3>Current Menu:</h3>
      {foods.map(f => (
        <div key={f._id}>
          <strong>{f.name}</strong> - ${f.price}
          <button onClick={() => deleteFood(f._id)} style={{ marginLeft: '10px' }}>Delete</button>
        </div>
      ))}
    </div>
  );
}

export default AdminDashboard;
