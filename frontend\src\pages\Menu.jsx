import React, { useEffect, useState } from 'react';
import axios from 'axios';
import { useDispatch } from 'react-redux';
import { addToCart } from '../store';

function Menu() {
  const [foods, setFoods] = useState([]);
  const dispatch = useDispatch();

  useEffect(() => {
    axios.get('http://localhost:5000/api/foods').then(res => setFoods(res.data));
  }, []);

  return (
    <div style={{ padding: '2rem' }}>
      <h2>Our Menu</h2>
      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '20px' }}>
        {foods.map(food => (
          <div key={food._id} style={{ border: '1px solid #ccc', padding: '10px', width: '200px' }}>
            <img src={food.image} alt={food.name} style={{ width: '100%' }} />
            <h4>{food.name}</h4>
            <p>{food.description}</p>
            <strong>${food.price}</strong><br />
            <button onClick={() => dispatch(addToCart(food))}>Add to Cart</button>
          </div>
        ))}
      </div>
    </div>
  );
}

export default Menu;
