<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Food Ordering System</title>
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://unpkg.com/react-router-dom@6.8.0/dist/umd/react-router-dom.development.js"></script>
    <style>
        body {
            margin: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
                'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
                sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .navbar {
            background-color: #333;
            overflow: hidden;
            padding: 1rem;
        }

        .navbar a {
            float: left;
            display: block;
            color: white;
            text-align: center;
            padding: 14px 20px;
            text-decoration: none;
        }

        .navbar a:hover {
            background-color: #ddd;
            color: black;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .hero {
            text-align: center;
            padding: 50px 0;
            background-color: #f8f9fa;
        }

        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            color: #333;
        }

        .hero p {
            font-size: 1.2rem;
            color: #666;
            margin-bottom: 2rem;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .btn:hover {
            background-color: #0056b3;
        }

        .form-container {
            max-width: 400px;
            margin: 50px auto;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 3px;
            box-sizing: border-box;
        }

        .menu-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .menu-item {
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 20px;
            text-align: center;
        }

        .menu-item h3 {
            margin-top: 0;
        }

        .price {
            font-size: 1.2rem;
            font-weight: bold;
            color: #007bff;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;
        const { BrowserRouter, Routes, Route, Link, useNavigate } = ReactRouterDOM;

        // Simple Navbar Component
        function Navbar() {
            return (
                <nav className="navbar">
                    <Link to="/">Home</Link>
                    <Link to="/menu">Menu</Link>
                    <Link to="/cart">Cart</Link>
                    <Link to="/login">Login</Link>
                    <Link to="/register">Register</Link>
                    <Link to="/admin">Admin</Link>
                </nav>
            );
        }

        // Home Component
        function Home() {
            return (
                <div>
                    <div className="hero">
                        <h1>Welcome to Food Ordering System</h1>
                        <p>Delicious food delivered to your doorstep</p>
                        <Link to="/menu" className="btn">Order Now</Link>
                    </div>
                </div>
            );
        }

        // Login Component
        function Login() {
            const [email, setEmail] = useState('');
            const [password, setPassword] = useState('');

            const handleSubmit = (e) => {
                e.preventDefault();
                console.log('Login attempt:', { email, password });
                // Add login logic here
            };

            return (
                <div className="container">
                    <div className="form-container">
                        <h2>Login</h2>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Email:</label>
                                <input
                                    type="email"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Password:</label>
                                <input
                                    type="password"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    required
                                />
                            </div>
                            <button type="submit" className="btn">Login</button>
                        </form>
                    </div>
                </div>
            );
        }

        // Register Component
        function Register() {
            const [formData, setFormData] = useState({
                name: '',
                email: '',
                password: '',
                confirmPassword: ''
            });

            const handleSubmit = (e) => {
                e.preventDefault();
                console.log('Register attempt:', formData);
                // Add registration logic here
            };

            const handleChange = (e) => {
                setFormData({
                    ...formData,
                    [e.target.name]: e.target.value
                });
            };

            return (
                <div className="container">
                    <div className="form-container">
                        <h2>Register</h2>
                        <form onSubmit={handleSubmit}>
                            <div className="form-group">
                                <label>Name:</label>
                                <input
                                    type="text"
                                    name="name"
                                    value={formData.name}
                                    onChange={handleChange}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Email:</label>
                                <input
                                    type="email"
                                    name="email"
                                    value={formData.email}
                                    onChange={handleChange}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Password:</label>
                                <input
                                    type="password"
                                    name="password"
                                    value={formData.password}
                                    onChange={handleChange}
                                    required
                                />
                            </div>
                            <div className="form-group">
                                <label>Confirm Password:</label>
                                <input
                                    type="password"
                                    name="confirmPassword"
                                    value={formData.confirmPassword}
                                    onChange={handleChange}
                                    required
                                />
                            </div>
                            <button type="submit" className="btn">Register</button>
                        </form>
                    </div>
                </div>
            );
        }

        // Menu Component
        function Menu() {
            const [menuItems, setMenuItems] = useState([
                { _id: '1', name: 'Pizza Margherita', price: 12.99, description: 'Classic pizza with tomato and mozzarella' },
                { _id: '2', name: 'Burger Deluxe', price: 9.99, description: 'Juicy beef burger with all the fixings' },
                { _id: '3', name: 'Caesar Salad', price: 7.99, description: 'Fresh romaine lettuce with caesar dressing' },
                { _id: '4', name: 'Pasta Carbonara', price: 11.99, description: 'Creamy pasta with bacon and parmesan' }
            ]);

            const addToCart = (item) => {
                console.log('Adding to cart:', item);
                // Add cart logic here
            };

            return (
                <div className="container">
                    <h1>Our Menu</h1>
                    <div className="menu-grid">
                        {menuItems.map(item => (
                            <div key={item._id} className="menu-item">
                                <h3>{item.name}</h3>
                                <p>{item.description}</p>
                                <div className="price">${item.price}</div>
                                <button className="btn" onClick={() => addToCart(item)}>
                                    Add to Cart
                                </button>
                            </div>
                        ))}
                    </div>
                </div>
            );
        }

        // Cart Component
        function Cart() {
            const [cartItems, setCartItems] = useState([]);

            return (
                <div className="container">
                    <h1>Your Cart</h1>
                    {cartItems.length === 0 ? (
                        <p>Your cart is empty</p>
                    ) : (
                        <div>
                            {cartItems.map(item => (
                                <div key={item._id}>
                                    <h3>{item.name}</h3>
                                    <p>Quantity: {item.qty}</p>
                                    <p>Price: ${item.price * item.qty}</p>
                                </div>
                            ))}
                        </div>
                    )}
                </div>
            );
        }

        // Admin Dashboard Component
        function AdminDashboard() {
            return (
                <div className="container">
                    <h1>Admin Dashboard</h1>
                    <p>Manage your restaurant from here</p>
                    <div>
                        <h3>Quick Actions</h3>
                        <button className="btn">Add New Menu Item</button>
                        <button className="btn">View Orders</button>
                        <button className="btn">Manage Users</button>
                    </div>
                </div>
            );
        }

        // Main App Component
        function App() {
            return (
                <BrowserRouter>
                    <Navbar />
                    <Routes>
                        <Route path="/" element={<Home />} />
                        <Route path="/login" element={<Login />} />
                        <Route path="/register" element={<Register />} />
                        <Route path="/menu" element={<Menu />} />
                        <Route path="/cart" element={<Cart />} />
                        <Route path="/admin" element={<AdminDashboard />} />
                    </Routes>
                </BrowserRouter>
            );
        }

        // Render the app
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
</body>
</html>
