import { configureStore, createSlice } from '@reduxjs/toolkit';
import { Provider } from 'react-redux';

const cartSlice = createSlice({
  name: 'cart',
  initialState: [],
  reducers: {
    addToCart: (state, action) => {
      const item = action.payload;
      const exists = state.find(x => x._id === item._id);
      if (exists) {
        return state.map(x => x._id === item._id ? { ...x, qty: x.qty + 1 } : x);
      } else {
        return [...state, { ...item, qty: 1 }];
      }
    },
    removeFromCart: (state, action) => {
      return state.filter(x => x._id !== action.payload);
    },
    clearCart: () => []
  }
});

export const { addToCart, removeFromCart, clearCart } = cartSlice.actions;

const store = configureStore({
  reducer: {
    cart: cartSlice.reducer
  }
});

export const AppProvider = ({ children }) => <Provider store={store}>{children}</Provider>;
