const express = require('express');
const router = express.Router();
const Food = require('../models/Food');
const { protect, adminOnly } = require('../middleware/authMiddleware');

// Get all foods
router.get('/foods', protect, adminOnly, async (req, res) => {
  const foods = await Food.find();
  res.json(foods);
});

// Add new food
router.post('/foods', protect, adminOnly, async (req, res) => {
  const newFood = new Food(req.body);
  await newFood.save();
  res.json({ message: 'Food added', food: newFood });
});

// Delete food
router.delete('/foods/:id', protect, adminOnly, async (req, res) => {
  await Food.findByIdAndDelete(req.params.id);
  res.json({ message: 'Food deleted' });
});

module.exports = router;
