import React, { useState } from 'react';
import axios from 'axios';

function Register() {
  const [formData, setFormData] = useState({ name: '', email: '', password: '' });

  const registerUser = async (e) => {
    e.preventDefault();
    const { data } = await axios.post('http://localhost:5000/api/users/register', formData);
    localStorage.setItem('userInfo', JSON.stringify(data));
    alert('Registered Successfully');
  };

  return (
    <form onSubmit={registerUser}>
      <h2>Signup</h2>
      <input type="text" placeholder="Name" onChange={e => setFormData({ ...formData, name: e.target.value })} />
      <input type="email" placeholder="Email" onChange={e => setFormData({ ...formData, email: e.target.value })} />
      <input type="password" placeholder="Password" onChange={e => setFormData({ ...formData, password: e.target.value })} />
      <button type="submit">Register</button>
    </form>
  );
}

export default Register;
